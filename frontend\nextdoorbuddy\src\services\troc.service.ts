import api from './api';

export interface AnnonceTroc {
    id?: number;
    titre: string;
    description: string;
    objet_propose: string;
    objet_recherche: string;
    image_url?: string;
    date_publication?: string;
    quartier_id?: number;
    utilisateur_id?: number;
    statut?: 'active' | 'inactive';
    nom?: string; // Nom de l'utilisateur (pour les annonces avec jointure)
    prenom?: string; // Prénom de l'utilisateur (pour les annonces avec jointure)
}

export const trocService = {
    // Récupérer toutes les annonces du quartier de l'utilisateur
    async getAllTrocs(): Promise<AnnonceTroc[]> {
        try {
            const data = await api.get('/troc');
            return data;
        } catch (error) {
            console.error('Erreur lors de la récupération des annonces de troc:', error);
            throw error;
        }
    },

    // Récupérer les annonces de l'utilisateur connecté
    async getMyTrocs(): Promise<AnnonceTroc[]> {
        try {
            const data = await api.get('/troc/my-trocs');
            return data;
        } catch (error) {
            console.error('Erreur lors de la récupération de mes annonces:', error);
            throw error;
        }
    },

    // Créer une nouvelle annonce
    async createTroc(trocData: Omit<AnnonceTroc, 'id' | 'date_publication' | 'quartier_id' | 'utilisateur_id' | 'statut'>): Promise<{ id: number }> {
        try {
            const data = await api.post('/troc', trocData);
            return data;
        } catch (error) {
            console.error('Erreur lors de la création de l\'annonce:', error);
            throw error;
        }
    },

    // Mettre à jour une annonce
    async updateTroc(id: number, trocData: Partial<AnnonceTroc>): Promise<{ message: string }> {
        try {
            const data = await api.put(`/troc/${id}`, trocData);
            return data;
        } catch (error) {
            console.error('Erreur lors de la mise à jour de l\'annonce:', error);
            throw error;
        }
    },

    // Supprimer une annonce
    async deleteTroc(id: number): Promise<{ message: string }> {
        try {
            const data = await api.delete(`/troc/${id}`);
            return data;
        } catch (error) {
            console.error('Erreur lors de la suppression de l\'annonce:', error);
            throw error;
        }
    }
};

export default trocService;
