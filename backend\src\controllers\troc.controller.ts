import { Request, Response } from 'express';
import { AnnonceTrocModel, AnnonceTroc } from '../models/annonceTroc.model.js';
import { UserModel } from '../models/user.model.js';

export const createTroc = async (req: Request, res: Response) => {
    try {
        const utilisateurId = (req as any).user.id

        const user = await UserModel.findById(utilisateurId)
        if (!user || !user.quartier_id) {
            return res.status(400).json({ message: 'Utilisateur invalide ou non rattaché à un quartier.' })
        }

        const newAnnonce: AnnonceTroc = {
            titre: req.body.titre,
            description: req.body.description,
            objet_propose: req.body.objet_propose,
            objet_recherche: req.body.objet_recherche,
            image_url: req.body.image_url || null,
            date_publication: new Date(),
            utilisateur_id: utilisateurId,
            quartier_id: user.quartier_id,
            statut: 'active',
            type_annonce: req.body.type_annonce || 'offre',
            prix: req.body.prix || null,
            budget_max: req.body.budget_max || null,
            etat_produit: req.body.etat_produit || null,
            categorie: req.body.categorie || null,
            urgence: req.body.urgence || null,
            mode_echange: req.body.mode_echange || 'vente',
            criteres_specifiques: req.body.criteres_specifiques || null,
            disponibilite: req.body.disponibilite || null,
        };

        const newId = await AnnonceTrocModel.create(newAnnonce)
        return res.status(201).json({ id: newId })
    } catch (error) {
        console.error('Erreur lors de la création de l\'annonce de troc :', error)
        return res.status(500).json({ message: 'Erreur serveur' })
    }
}

export const getTrocByUserQuartier = async (req: Request, res: Response) => {
    try {
        const utilisateurId = (req as any).user.id

        const user = await UserModel.findById(utilisateurId)
        if (!user || !user.quartier_id) {
            return res.status(400).json({ message: 'Utilisateur invalide ou non rattaché à un quartier.' })
        }

        const annonces = await AnnonceTrocModel.findByQuartierWithUser(user.quartier_id)
        return res.status(200).json(annonces)
    } catch (error) {
        console.error('Erreur lors de la récupération des annonces de troc :', error)
        return res.status(500).json({ message: 'Erreur serveur' })
    }
};

export const getUserTrocs = async (req: Request, res: Response) => {
    try {
        const utilisateurId = (req as any).user.id
        const annonces = await AnnonceTrocModel.findByUser(utilisateurId)
        return res.status(200).json(annonces)
    } catch (error) {
        console.error('Erreur lors de la récupération des annonces de l\'utilisateur :', error)
        return res.status(500).json({ message: 'Erreur serveur' })
    }
};

export const updateTroc = async (req: Request, res: Response) => {
    try {
        const utilisateurId = (req as any).user.id
        const trocId = parseInt(req.params.id)

        // Vérifier que l'annonce appartient à l'utilisateur
        const existingTroc = await AnnonceTrocModel.findById(trocId)
        if (!existingTroc || existingTroc.utilisateur_id !== utilisateurId) {
            return res.status(403).json({ message: 'Accès non autorisé à cette annonce.' })
        }

        const updatedData = {
            titre: req.body.titre,
            description: req.body.description,
            objet_propose: req.body.objet_propose,
            objet_recherche: req.body.objet_recherche,
            image_url: req.body.image_url || null,
            type_annonce: req.body.type_annonce,
            prix: req.body.prix || null,
            budget_max: req.body.budget_max || null,
            etat_produit: req.body.etat_produit || null,
            categorie: req.body.categorie || null,
            urgence: req.body.urgence || null,
            mode_echange: req.body.mode_echange || null,
            criteres_specifiques: req.body.criteres_specifiques || null,
            disponibilite: req.body.disponibilite || null,
        }

        const success = await AnnonceTrocModel.update(trocId, updatedData)
        if (success) {
            return res.status(200).json({ message: 'Annonce mise à jour avec succès' })
        } else {
            return res.status(404).json({ message: 'Annonce non trouvée' })
        }
    } catch (error) {
        console.error('Erreur lors de la mise à jour de l\'annonce :', error)
        return res.status(500).json({ message: 'Erreur serveur' })
    }
};

export const deleteTroc = async (req: Request, res: Response) => {
    try {
        const utilisateurId = (req as any).user.id
        const trocId = parseInt(req.params.id)

        // Vérifier que l'annonce appartient à l'utilisateur
        const existingTroc = await AnnonceTrocModel.findById(trocId)
        if (!existingTroc || existingTroc.utilisateur_id !== utilisateurId) {
            return res.status(403).json({ message: 'Accès non autorisé à cette annonce.' })
        }

        const success = await AnnonceTrocModel.delete(trocId)
        if (success) {
            return res.status(200).json({ message: 'Annonce supprimée avec succès' })
        } else {
            return res.status(404).json({ message: 'Annonce non trouvée' })
        }
    } catch (error) {
        console.error('Erreur lors de la suppression de l\'annonce :', error)
        return res.status(500).json({ message: 'Erreur serveur' })
    }
};

