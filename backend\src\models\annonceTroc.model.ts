import pool from '../config/db.js';

export interface AnnonceTroc {
    id?: number;
    titre: string;
    description: string;
    objet_propose: string;
    objet_recherche: string;
    image_url?: string;
    date_publication: Date;
    quartier_id: number;
    utilisateur_id: number;
    statut: 'active' | 'inactive';
}

export class AnnonceTrocModel {
    static async create(data: AnnonceTroc): Promise<number> {
        try {
            const result = await pool.query(
                `INSERT INTO "AnnonceTroc" 
            (titre, description, objet_propose, objet_recherche, image_url, date_publication, quartier_id, utilisateur_id, statut)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id`,
                [
                    data.titre,
                    data.description,
                    data.objet_propose,
                    data.objet_recherche,
                    data.image_url || null,
                    data.date_publication,
                    data.quartier_id,
                    data.utilisateur_id,
                    data.statut
                ]
            );
            return result.rows[0].id;
        } catch (error) {
            console.error('Erreur lors de la création de l\'annonce de troc :', error);
            throw error;
        }
    }

    static async findByQuartier(quartierId: number): Promise<AnnonceTroc[]> {
        try {
            const result = await pool.query(
                `SELECT * FROM "AnnonceTroc"
             WHERE quartier_id = $1 AND statut = 'active'
             ORDER BY date_publication DESC`,
                [quartierId]
            );
            return result.rows;
        } catch (error) {
            console.error('Erreur lors de la récupération des annonces :', error);
            throw error;
        }
    }

}