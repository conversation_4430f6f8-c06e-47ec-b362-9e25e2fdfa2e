import express from 'express';
import { createTroc, getTrocByUserQuartier, getUserTrocs, updateTroc, deleteTroc } from '../controllers/troc.controller.js';
import { authenticateJWT } from '../middlewares/auth.middleware.js';

const router = express.Router();

// Routes pour les annonces de troc
router.post('/', authenticateJWT, createTroc);
router.get('/', authenticateJWT, getTrocByUserQuartier);
router.get('/my-trocs', authenticateJWT, getUserTrocs);
router.put('/:id', authenticateJWT, updateTroc);
router.delete('/:id', authenticateJWT, deleteTroc);

export default router;
