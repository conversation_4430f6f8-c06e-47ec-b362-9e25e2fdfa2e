import express from 'express';
import { handleImageUpload } from '../controllers/upload.controller.js';
import { authenticateJWT } from '../middlewares/auth.middleware.js';

const router = express.Router();

// Route pour l'upload d'images (authentifiée) - version simplifiée
router.post('/image', authenticateJWT, handleImageUpload);

// Route de test sans authentification
router.post('/test', handleImageUpload);

export default router;
