import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import jwtConfig from '../config/jwt.js';
import { UserModel } from '../models/user.model.js';

declare global {
    namespace Express {
        interface Request {
            user?: any;
        }
    }
}

export const authenticateJWT = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const authHeader = req.headers.authorization;

        if (!authHeader) {
             res.status(401).json({ message: 'Accès non autorisé. Token manquant.' });
        }

        const token = authHeader.split(' ')[1];

        jwt.verify(token, jwtConfig.accessToken.secret, async (err: any, decoded: any) => {
            if (err) {
                 res.status(403).json({ message: 'Token invalide ou expiré.' });
            }

            const user = await UserModel.findById(decoded.userId);
            if (!user) {
                 res.status(404).json({ message: 'Utilisateur non trouvé.' });
            }

            req.user = user;
            next();
        });
    } catch (error) {
        console.error('Erreur d\'authentification:', error);
         res.status(500).json({ message: 'Erreur serveur lors de l\'authentification.' });
    }
};

export const isAdmin = (req: Request, res: Response, next: NextFunction) => {
    if (req.user && req.user.role === 'admin') {
        next();
    } else {
        res.status(403).json({ message: 'Accès refusé. Droits d\'administrateur requis.' });
    }
};

export default { authenticateJWT, isAdmin };
