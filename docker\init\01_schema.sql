-- Quartier
CREATE TABLE "Quartier" (
  id SERIAL PRIMARY KEY,
  nom_quartier VARCHAR(100) NOT NULL,
  ville VARCHAR(100),
  code_postal VARCHAR(10),
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Utilisateur
CREATE TYPE user_role AS ENUM ('user', 'admin');

CREATE TABLE "Utilisateur" (
  id SERIAL PRIMARY KEY,
  nom VARCHAR(100) NOT NULL,
  prenom VARCHAR(100),
  email VARCHAR(255) UNIQUE,
  password VARCHAR(255) NOT NULL,
  adresse TEXT,
  date_naissance DATE,
  telephone VARCHAR(15),
  quartier_id INT,
  role user_role DEFAULT 'user',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIG<PERSON> KEY (quartier_id) REFERENCES "Quartier"(id)
);

-- RefreshToken pour l'authentification
CREATE TABLE "RefreshToken" (
  id SERIAL PRIMARY KEY,
  user_id INT NOT NULL,
  token VARCHAR(255) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  revoked BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (user_id) REFERENCES "Utilisateur"(id) ON DELETE CASCADE
);

-- Evenement
CREATE TABLE "Evenement" (
  id SERIAL PRIMARY KEY,
  organisateur_id INT,
  nom VARCHAR(255),
  description TEXT,
  date_evenement TIMESTAMP,
  lieu VARCHAR(255),
  type_evenement VARCHAR(100),
  FOREIGN KEY (organisateur_id) REFERENCES "Utilisateur"(id)
);

-- Participation
CREATE TABLE "Participation" (
  id SERIAL PRIMARY KEY,
  utilisateur_id INT,
  evenement_id INT,
  date_inscription TIMESTAMP,
  FOREIGN KEY (utilisateur_id) REFERENCES "Utilisateur"(id),
  FOREIGN KEY (evenement_id) REFERENCES "Evenement"(id)
);

-- Relation (type: ami, voisin, etc.)
CREATE TABLE "Relation" (
  id SERIAL PRIMARY KEY,
  utilisateur1_id INT,
  utilisateur2_id INT,
  type_relation VARCHAR(100),
  date_debut DATE,
  FOREIGN KEY (utilisateur1_id) REFERENCES "Utilisateur"(id),
  FOREIGN KEY (utilisateur2_id) REFERENCES "Utilisateur"(id)
);

-- Relation Utilisateur-Quartier (pour les quartiers secondaires)
CREATE TABLE "UtilisateurQuartier" (
  id SERIAL PRIMARY KEY,
  utilisateur_id INT NOT NULL,
  quartier_id INT NOT NULL,
  est_principal BOOLEAN DEFAULT FALSE,
  date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  statut VARCHAR(20) DEFAULT 'actif',
  FOREIGN KEY (utilisateur_id) REFERENCES "Utilisateur"(id) ON DELETE CASCADE,
  FOREIGN KEY (quartier_id) REFERENCES "Quartier"(id) ON DELETE CASCADE,
  UNIQUE(utilisateur_id, quartier_id)
);

-- Table des annonces de troc
CREATE TABLE "AnnonceTroc" (
  id SERIAL PRIMARY KEY,
  titre VARCHAR(255) NOT NULL,
  description TEXT,
  objet_propose VARCHAR(255) NOT NULL,
  objet_recherche VARCHAR(255) NOT NULL,
  image_url TEXT,
  date_publication TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  quartier_id INTEGER REFERENCES "Quartier"(id),
  utilisateur_id INTEGER REFERENCES "Utilisateur"(id),
  statut VARCHAR(20) DEFAULT 'active' CHECK (statut IN ('active', 'inactive')),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Fonction pour mettre à jour le champ updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour le champ updated_at dans la table Utilisateur
CREATE TRIGGER update_utilisateur_updated_at
BEFORE UPDATE ON "Utilisateur"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger pour mettre à jour le champ updated_at dans la table Quartier
CREATE TRIGGER update_quartier_updated_at
BEFORE UPDATE ON "Quartier"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
