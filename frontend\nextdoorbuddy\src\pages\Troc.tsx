import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Header';
import { trocService, AnnonceTroc } from '../services/troc.service';

function Troc() {
    const [annonces, setAnnonces] = useState<AnnonceTroc[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const loadAnnonces = async () => {
        try {
            setLoading(true);
            const data = await trocService.getAllTrocs();
            setAnnonces(data);
            setError(null);
        } catch (err) {
            setError('Erreur lors du chargement des annonces');
            console.error('Erreur:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadAnnonces();
    }, []);

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('fr-FR', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        });
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-100">
                <Header />
                <div className="container mx-auto p-6">
                    <div className="text-center">Chargement des annonces...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-100">
            <Header />
            <div className="container mx-auto p-6">
                <div className="mb-6 flex items-center justify-between">
                    <h1 className="text-2xl font-bold text-gray-800">Trocs du quartier</h1>
                    <div className="flex space-x-4">
                        <Link
                            to="/trocs/my-trocs"
                            className="rounded-md bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
                        >
                            Mes annonces
                        </Link>
                        <Link
                            to="/trocs/create"
                            className="rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                        >
                            Créer une annonce
                        </Link>
                    </div>
                </div>

                {error && (
                    <div className="mb-6 rounded-lg bg-red-50 p-4 text-red-700">
                        {error}
                    </div>
                )}

                {annonces.length === 0 ? (
                    <div className="rounded-lg bg-white p-8 text-center shadow">
                        <h3 className="mb-4 text-lg font-semibold text-gray-600">
                            Aucune annonce de troc dans votre quartier
                        </h3>
                        <p className="mb-6 text-gray-500">
                            Soyez le premier à proposer un échange !
                        </p>
                        <Link
                            to="/trocs/create"
                            className="rounded-md bg-blue-500 px-6 py-3 text-white hover:bg-blue-600"
                        >
                            Créer la première annonce
                        </Link>
                    </div>
                ) : (
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {annonces.map((annonce) => (
                            <div key={annonce.id} className="rounded-lg bg-white p-6 shadow hover:shadow-lg transition-shadow">
                                <div className="mb-4">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-2">
                                        {annonce.titre}
                                    </h3>
                                    <p className="text-sm text-gray-500 mb-2">
                                        Par {annonce.prenom} {annonce.nom} • {formatDate(annonce.date_publication!)}
                                    </p>
                                </div>

                                {annonce.image_url && (
                                    <div className="mb-4">
                                        <img
                                            src={annonce.image_url}
                                            alt={annonce.titre}
                                            className="w-full h-48 object-cover rounded-lg"
                                        />
                                    </div>
                                )}

                                <div className="mb-4">
                                    <p className="text-gray-700 mb-3">{annonce.description}</p>

                                    <div className="space-y-2">
                                        {annonce.objet_propose && (
                                            <div className="flex items-center">
                                                <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                                <span className="text-sm">
                                                    <strong>Propose:</strong> {annonce.objet_propose}
                                                </span>
                                            </div>
                                        )}
                                        {annonce.objet_recherche && (
                                            <div className="flex items-center">
                                                <span className="inline-block w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                                                <span className="text-sm">
                                                    <strong>Recherche:</strong> {annonce.objet_recherche}
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                <div className="flex justify-end">
                                    <button className="rounded-md bg-blue-500 px-4 py-2 text-sm text-white hover:bg-blue-600">
                                        Contacter
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}

export default Troc;
