import { useState, useEffect } from 'react';

function Troc() {
    const [form, setForm] = useState({
        titre: '',
        description: '',
        objet_propose: '',
        objet_recherche: '',
        image_url: ''
    });
    const [annonces, setAnnonces] = useState([]);
    const token = localStorage.getItem('token');

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setForm({ ...form, [e.target.name]: e.target.value });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const res = await fetch('http://localhost:3001/api/troc', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`
            },
            body: JSON.stringify(form)
        });
        if (res.ok) {
            setForm({ titre: '', description: '', objet_propose: '', objet_recherche: '', image_url: '' });
            loadAnnonces(); // rechargement
        }
    };

    const loadAnnonces = async () => {
        const res = await fetch('http://localhost:3001/api/troc', {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });
        if (res.ok) {
            const data = await res.json();
            setAnnonces(data);
        }
    };

    useEffect(() => {
        loadAnnonces();
    }, []);

    return (
        <div className="p-4">
            <h2 className="text-xl font-bold mb-2">Nouvelle annonce de troc</h2>
            <form onSubmit={handleSubmit} className="space-y-2">
                <input name="titre" placeholder="Titre" value={form.titre} onChange={handleChange} className="border p-1 w-full" />
                <textarea name="description" placeholder="Description" value={form.description} onChange={handleChange} className="border p-1 w-full" />
                <input name="objet_propose" placeholder="Objet proposé" value={form.objet_propose} onChange={handleChange} className="border p-1 w-full" />
                <input name="objet_recherche" placeholder="Objet recherché" value={form.objet_recherche} onChange={handleChange} className="border p-1 w-full" />
                <input name="image_url" placeholder="URL image (optionnel)" value={form.image_url} onChange={handleChange} className="border p-1 w-full" />
                <button type="submit" className="bg-blue-500 text-white px-4 py-1 rounded">Publier</button>
            </form>

            <h3 className="text-lg font-bold mt-6">Annonces de mon quartier</h3>
            <ul className="mt-2 space-y-2">
                {annonces.map((a: any) => (
                    <li key={a.id} className="border p-2">
                        <strong>{a.titre}</strong> — {a.objet_propose}
                        <div>{a.description}</div>
                        {a.image_url && <img src={a.image_url} alt="" className="mt-1 max-h-48" />}
                    </li>
                ))}
            </ul>
        </div>
    );
}

export default Troc;
